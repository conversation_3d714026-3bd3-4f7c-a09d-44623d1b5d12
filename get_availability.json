[{"headers": {"host": "n8n.srv908169.hstgr.cloud", "user-agent": "Python/3.12 aiohttp/3.11.16", "content-length": "66", "accept": "*/*", "accept-encoding": "gzip, deflate", "content-type": "application/json", "x-forwarded-for": "***********", "x-forwarded-host": "n8n.srv908169.hstgr.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "8fe981fb3a8a", "x-real-ip": "***********"}, "params": {}, "query": {}, "body": {"timestamp": "2025-07-30T00:00:00Z", "tool": "get_availability "}, "webhookUrl": "https://n8n.srv908169.hstgr.cloud/webhook-test/4fe15a31-6365-4b96-a3d5-3b02bbe3d31a", "executionMode": "test"}]