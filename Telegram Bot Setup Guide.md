# Telegram AI Bot Setup Guide

## Overview
Your WhatsApp workflow has been successfully converted to a Telegram implementation. The new workflow (`Telegram AI Bot Workflow.json`) maintains all the original functionality:

- ✅ **Text Messages** - AI responses with memory
- ✅ **Voice Messages** - Transcription + AI responses  
- ✅ **Audio Files** - Transcription + AI responses
- ✅ **Images** - Vision analysis + AI responses
- ✅ **Memory** - Contextual conversations per user
- ✅ **Audio Responses** - TTS for voice message replies

## Step 1: Create Telegram Bot

1. **Open Telegram** and search for `@BotFather`
2. **Start conversation** with BotFather
3. **Create new bot**: Send `/newbot`
4. **Choose bot name**: e.g., "PhilBot AI Assistant"
5. **Choose username**: e.g., "philbot_ai_bot" (must end with 'bot')
6. **Save the token**: Bot<PERSON><PERSON> will give you a token like `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`

## Step 2: Configure n8n Credentials

1. **Open n8n** and go to **Credentials**
2. **Create new credential** → **Telegram API**
3. **Enter your bot token** from Step 1
4. **Name it**: "Telegram Bot API"
5. **Save the credential**
6. **Copy the credential ID** (you'll need this for Step 3)

## Step 3: Update Workflow Credentials

1. **Open** `Telegram AI Bot Workflow.json` in a text editor
2. **Find all instances** of `"id": "TELEGRAM_CREDENTIALS_ID"`
3. **Replace** `TELEGRAM_CREDENTIALS_ID` with your actual credential ID from Step 2
4. **Save the file**

## Step 4: Import Workflow to n8n

1. **Open n8n**
2. **Click** "Import from File"
3. **Select** `Telegram AI Bot Workflow.json`
4. **Verify** all credentials are properly linked
5. **Activate** the workflow

## Step 5: Test Your Bot

### Text Messages
- Send any text message to your bot
- Should receive AI response with memory

### Voice Messages  
- Send voice message to your bot
- Should transcribe and respond with audio (if original was voice)

### Images
- Send image with or without caption
- Should analyze image and provide description

## Key Changes Made

### 1. **Trigger Node**
- `WhatsApp Trigger` → `Telegram Trigger`
- Updated to listen for Telegram message updates

### 2. **Message Structure**
- **WhatsApp**: `$json.messages[0].text.body`
- **Telegram**: `$json.message.text`

### 3. **User Identification**
- **WhatsApp**: Phone number (`$json.messages[0].from`)
- **Telegram**: User ID (`$json.message.from.id`)

### 4. **File Handling**
- **WhatsApp**: Media URL retrieval + HTTP download
- **Telegram**: Direct file API access

### 5. **Response Nodes**
- All WhatsApp send nodes replaced with Telegram equivalents
- Proper chat ID targeting for responses

## Troubleshooting

### Bot Not Responding
1. Check if workflow is **activated**
2. Verify **webhook URL** is accessible
3. Check **credential configuration**

### File Download Issues
1. Ensure bot token has proper permissions
2. Check file size limits (Telegram: 50MB max)

### Memory Not Working
1. Verify user ID extraction: `$('Telegram Trigger').item.json.message.from.id`
2. Check Simple Memory node configuration

## Next Steps
1. Complete the credential setup (Step 2-3)
2. Import and test the workflow
3. Customize the AI system message if needed
4. Add additional features as required

The workflow is now ready for Telegram! All the sophisticated AI capabilities from your WhatsApp bot are preserved.
