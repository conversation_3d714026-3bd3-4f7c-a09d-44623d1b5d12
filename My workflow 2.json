{"name": "Telegram AI Bot", "nodes": [{"parameters": {"updates": ["message"], "options": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [-544, -160], "id": "45e653b3-f69e-477b-9131-1654fc8a6538", "name": "<PERSON>eg<PERSON>", "webhookId": "1ae43816-b306-4d94-af8c-a32d0336a573", "credentials": {"telegramApi": {"id": "TELEGRAM_CREDENTIALS_ID", "name": "Telegram Bot API"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "=Du bist ein hilfsbereiter Assistent namens PhilBot. Antworte in einem natürlichen und freundlichen Ton. \n\nDu sprichst derzeit mit {{ $('Telegram Trigger').item.json.message.from.first_name || $('Telegram Trigger').item.json.message.from.username || 'Benutzer' }}. \n\nDas aktuelle Datum und die aktuelle Zeit sind {{ $now.toISO() }}\n\nDein Output ist niemals länger als 4096 Zeichen. "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [800, -160], "id": "d18aaa1c-41b5-43d5-a5b4-c62ad1bc1828", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [832, 64], "id": "29abdd90-305c-4427-b26e-5cccd689d0af", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "hVGX5GFxSlxjXJPm", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.from.id }}", "contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [960, 64], "id": "2ce50931-cddc-4304-a3e4-675d9eaefc4e", "name": "Simple Memory"}, {"parameters": {"assignments": {"assignments": [{"id": "b826b641-cab0-4a63-88a2-304d288a42f1", "name": "text", "value": "={{ $json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [576, -160], "id": "d72f0246-f1a3-422b-bc79-5ffb7278db62", "name": "Text only prompt"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.voice }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "id": "c145d044-edf6-4b81-98ac-20e7bc1f0dd0"}, {"leftValue": "={{ $json.message.audio }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "id": "c145d044-edf6-4b81-98ac-20e7bc1f0dd1"}], "combinator": "or"}, "renameOutput": true, "outputKey": "Audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7cf16363-1ddc-4965-a058-d46482233e51", "leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "674a7983-ebac-402b-9099-68e7cfe9c69a", "leftValue": "={{ $json.message.photo }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-320, -176], "id": "6a0ca34c-998e-440e-a44e-6e37a30254ec", "name": "Switch"}, {"parameters": {"resource": "file", "operation": "get", "fileId": "={{ $json.message.voice ? $json.message.voice.file_id : $json.message.audio.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [-96, -352], "id": "1df6fae5-63d0-43fb-9681-07cf73320210", "name": "Get Audio File", "credentials": {"telegramApi": {"id": "TELEGRAM_CREDENTIALS_ID", "name": "Telegram Bot API"}}}, {"parameters": {"url": "=https://api.telegram.org/file/bot{{ $credentials.telegramApi.accessToken }}/{{ $json.file_path }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [128, -352], "id": "48ab472b-ab81-48f1-b8d1-16f57b13591f", "name": "Download Audio"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [352, -352], "id": "664459c4-c77a-4797-9ce0-5bf8631eca37", "name": "Transcribe a recording", "credentials": {"openAiApi": {"id": "0ZLqfpCxEhbkAjNA", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "44eeecd5-6354-45e7-8e2d-02b4474bb8f8", "name": "text", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [576, -352], "id": "fc0f1223-02ec-474a-a72f-1eeabbf129db", "name": "Audio prompt"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a3d88d46-7083-45de-a766-0f9a1f5d9b93", "leftValue": "={{ $('Tel<PERSON>ram Trigger').item.json.message.voice }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}, {"id": "a3d88d46-7083-45de-a766-0f9a1f5d9b94", "leftValue": "={{ $('Telegram Trigger').item.json.message.audio }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1200, -160], "id": "60c85c0b-e14d-4a73-9efa-c58d91833a4d", "name": "If"}, {"parameters": {"resource": "message", "operation": "sendMessage", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1424, -64], "id": "f7aa867b-e5e4-4b39-8617-6045e33fdb38", "name": "Respond with text", "credentials": {"telegramApi": {"id": "TELEGRAM_CREDENTIALS_ID", "name": "Telegram Bot API"}}}, {"parameters": {"resource": "audio", "input": "={{ $('AI Agent').item.json.output }}", "voice": "onyx", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1424, -256], "id": "a946a462-5a98-4a60-a77a-a5ddd35dfa8b", "name": "Generate audio", "credentials": {"openAiApi": {"id": "0ZLqfpCxEhbkAjNA", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// Loop over input items and change the MIME type of binary data\nfor (const item of $input.all()) {\n  // Check if the item has binary data\n  if (item.binary) {\n    // Find the binary property name (assuming there's at least one)\n    const binaryPropertyNames = Object.keys(item.binary);\n\n    for (const propName of binaryPropertyNames) {\n      // If the MIME type is 'audio/mp3', change it to 'audio/mpeg'\n      if (item.binary[propName].mimeType === \"audio/mp3\") {\n        item.binary[propName].mimeType = \"audio/mpeg\";\n      }\n    }\n  }\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1648, -256], "id": "97d16a54-58f5-4adc-84ec-b76fb0d2884e", "name": "Code"}, {"parameters": {"resource": "message", "operation": "sendAudio", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "binaryData": true, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1872, -256], "id": "c89e57b3-8d12-47c6-aa8c-d6cbddccd971", "name": "Respond with audio", "credentials": {"telegramApi": {"id": "TELEGRAM_CREDENTIALS_ID", "name": "Telegram Bot API"}}}, {"parameters": {"resource": "file", "operation": "get", "fileId": "={{ $json.message.photo[$json.message.photo.length - 1].file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [-96, 32], "id": "249b507f-71ea-4e27-a5b1-15b71e0f4021", "name": "Get Image File", "credentials": {"telegramApi": {"id": "TELEGRAM_CREDENTIALS_ID", "name": "Telegram Bot API"}}}, {"parameters": {"url": "=https://api.telegram.org/file/bot{{ $credentials.telegramApi.accessToken }}/{{ $json.file_path }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [128, 32], "id": "d6cd026c-384f-4fb4-b65a-68c99ac56ade", "name": "Download Image"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "text": "Beschreibe das Bild. ", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [352, 32], "id": "352025d9-74fb-45c3-bf0c-7f03364b8bea", "name": "Analyze image", "credentials": {"openAiApi": {"id": "0ZLqfpCxEhbkAjNA", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "cb1c5abe-cb4b-4a9c-8a2b-f48f5b55e725", "name": "text", "value": "=# Der Benutzer stellte uns das folgende Bild und Text zur Verfügung. \n\n## Bild Beschreibung:\n{{ $json.content }}\n\n## Beschreibung des Benutzers:\n{{ $('Telegram Trigger').item.json.message.caption || \"Beschreibe dieses Bild\"}}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [576, 32], "id": "272698c0-b8e2-414e-8831-c2b4f71bbfe7", "name": "Image + Text Prompt"}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "AI Agent": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Text only prompt": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Get Audio File", "type": "main", "index": 0}], [{"node": "Text only prompt", "type": "main", "index": 0}], [{"node": "Get Image File", "type": "main", "index": 0}]]}, "Get Audio File": {"main": [[{"node": "Download Audio", "type": "main", "index": 0}]]}, "Download Audio": {"main": [[{"node": "Transcribe a recording", "type": "main", "index": 0}]]}, "Transcribe a recording": {"main": [[{"node": "Audio prompt", "type": "main", "index": 0}]]}, "Audio prompt": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Generate audio", "type": "main", "index": 0}], [{"node": "Respond with text", "type": "main", "index": 0}]]}, "Generate audio": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Respond with audio", "type": "main", "index": 0}]]}, "Get Image File": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}, "Download Image": {"main": [[{"node": "Analyze image", "type": "main", "index": 0}]]}, "Analyze image": {"main": [[{"node": "Image + Text Prompt", "type": "main", "index": 0}]]}, "Image + Text Prompt": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "335636ce-ba42-4b8a-aa9a-90291a37765f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "25e6d607000fb7adb0485e08f8e882e6080fab07a6ac5aefca2c92a837e0c313"}, "id": "0XIlkbh9RspQgqb2", "tags": []}