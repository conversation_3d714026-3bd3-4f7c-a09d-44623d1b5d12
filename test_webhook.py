import requests
import json
import glob

def get_available_payloads():
    """Get all available JSON payload files"""
    json_files = glob.glob("*.json")
    return [f for f in json_files if f != "test_data.json"]

def display_payload_options(payloads):
    """Display available payload options to the user"""
    print("\nAvailable webhook payloads:")
    print("-" * 30)
    for i, payload in enumerate(payloads, 1):
        payload_name = payload.replace('.json', '').replace('_', ' ').title()
        print(f"{i}. {payload_name} ({payload})")
    print("-" * 30)

def load_payload_data(filename):
    """Load payload data from JSON file"""
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
        return data[0]  # Get the first (and only) item from the array
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in file '{filename}'.")
        return None

def main():
    # Get available payload files
    available_payloads = get_available_payloads()

    if not available_payloads:
        print("No JSON payload files found!")
        return

    # Display options and get user choice
    display_payload_options(available_payloads)

    while True:
        try:
            choice = int(input(f"\nSelect payload (1-{len(available_payloads)}): "))
            if 1 <= choice <= len(available_payloads):
                selected_file = available_payloads[choice - 1]
                break
            else:
                print(f"Please enter a number between 1 and {len(available_payloads)}")
        except ValueError:
            print("Please enter a valid number")

    # Load the selected payload
    webhook_data = load_payload_data(selected_file)
    if not webhook_data:
        return

    # Extract webhook information
    webhook_url = webhook_data["webhookUrl"]
    payload = webhook_data["body"]

    # Set headers
    headers = {
        "Content-Type": webhook_data["headers"]["content-type"]
    }

    print(f"\nUsing payload from: {selected_file}")
    print(f"Webhook URL: {webhook_url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print(f"Headers: {json.dumps(headers, indent=2)}")

    # Confirm before sending
    confirm = input("\nSend webhook? (y/n): ").lower().strip()
    if confirm != 'y':
        print("Webhook cancelled.")
        return

    # Send the webhook
    try:
        response = requests.post(webhook_url, data=json.dumps(payload), headers=headers)
        response.raise_for_status() # Raises an HTTPError for bad responses (4xx or 5xx)
        print(f"\nWebhook sent successfully! Status Code: {response.status_code}")
        print(f"Response Body: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"Error sending webhook: {e}")

if __name__ == "__main__":
    main()