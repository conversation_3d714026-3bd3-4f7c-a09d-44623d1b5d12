import requests
import json

# Load test data from test_data.json
with open('test_data.json', 'r') as f:
    test_data = json.load(f)

# Extract webhook data from the first (and only) item in the array
webhook_data = test_data[0]
webhook_url = webhook_data["webhookUrl"]
payload = webhook_data["body"]

# Use relevant headers from the test data
headers = {
    "Content-Type": webhook_data["headers"]["content-type"],
    "User-Agent": webhook_data["headers"]["user-agent"]
}

try:
    response = requests.post(webhook_url, data=json.dumps(payload), headers=headers)
    response.raise_for_status() # Raises an HTTPError for bad responses (4xx or 5xx)
    print(f"Webhook sent successfully! Status Code: {response.status_code}")
    print(f"Response Body: {response.text}")
except requests.exceptions.RequestException as e:
    print(f"Error sending webhook: {e}")