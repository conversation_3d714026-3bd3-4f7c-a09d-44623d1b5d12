import requests
import json

webhook_url = "https://n8n.srv908169.hstgr.cloud/webhook-test/4fe15a31-6365-4b96-a3d5-3b02bbe3d31a" # Replace with your actual n8n webhook URL
payload = {
    "event": "my_custom_event",
    "data": {
        "item_id": "ABC123",
        "status": "processed",
        "timestamp": "2025-07-29T17:50:00Z"
    },
    "metadata": {
        "source": "my_application"
    }
}

headers = {
    "Content-Type": "application/json"
}

try:
    response = requests.post(webhook_url, data=json.dumps(payload), headers=headers)
    response.raise_for_status() # Raises an HTTPError for bad responses (4xx or 5xx)
    print(f"Webhook sent successfully! Status Code: {response.status_code}")
    print(f"Response Body: {response.text}")
except requests.exceptions.RequestException as e:
    print(f"Error sending webhook: {e}")