# docker-compose.yml (Traefik und n8n - im Root-Verzeichnis)
version: "3.7"

services:
  traefik:
    image: "traefik:v2.10" # Nutze eine spezifische Version, z.B. v2.10
    container_name: traefik
    restart: always
    command:
      # --- API & Dashboard ---
      - "--api.dashboard=true"
      - "--api.insecure=true" # Nur für Entwicklung/lokalen Zugriff!

      # --- Providers (Docker) ---
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false" # Nur explizit gelabelte Services exposen
      - "--providers.docker.network=webproxy" # WICHTIG: Das gemeinsame Netzwerk überwachen lassen

      # --- Entrypoints ---
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"

      # --- HTTP zu HTTPS Umleitung (Global) ---
      - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
      - "--entrypoints.web.http.redirections.entrypoint.permanent=true"

      # --- Let's Encrypt / ACME Konfiguration ---
      - "--certificatesresolvers.mytlschallenge.acme.tlschallenge=true"
      - "--certificatesresolvers.mytlschallenge.acme.email=${SSL_EMAIL}" # Stellen Sie sicher, dass SSL_EMAIL gesetzt ist
      - "--certificatesresolvers.mytlschallenge.acme.storage=/letsencrypt/acme.json" # Speicherort für Zertifikate
    ports:
      - "80:80"    # HTTP-Entrypoint für Traefik
      - "443:443"  # HTTPS-Entrypoint für Traefik
      - "8080:8080" # Traefik Dashboard (NICHT nach außen exposen für Produktion, wenn insecure=true)
    volumes:
      # Traefik Datenverzeichnis für Let's Encrypt (und optional traefik.yml)
      - traefik_data:/letsencrypt # <--- NAMED VOLUME für persistente Daten
      - /var/run/docker.sock:/var/run/docker.sock:ro # Docker Socket für Service Discovery
    networks: # <--- WICHTIG: Service muss im Netzwerk sein
      - webproxy # <--- Container ist Teil dieses Netzwerks

  n8n:
    image: docker.n8n.io/n8nio/n8n:1.102.0 # Spezifische Version
    container_name: n8n_app
    restart: always
    # Der Port "127.0.0.1:5678:5678" veröffentlicht n8n nur auf localhost des Hosts.
    # Da Traefik proxyed, ist das okay, aber nicht unbedingt nötig,
    # wenn n8n nur über Traefik erreichbar sein soll.
    # Wenn du den 127.0.0.1 Teil weglässt und nur "- "5678:5678"" hast,
    # wäre n8n direkt über die VPS-IP:5678 erreichbar (nicht sicher).
    # Da Traefik den Port 5678 intern anspricht, brauchst du hier keine Host-Port-Veröffentlichung,
    # wenn du n8n NICHT direkt vom Host erreichen musst (nur über Traefik).
    # Wenn du es komplett über Traefik laufen lassen willst, entferne die 'ports:' Sektion hier.
    ports:
      - "127.0.0.1:5678:5678" # Nur von Host-localhost erreichbar
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`${SUBDOMAIN}.${DOMAIN_NAME}`)"
      - "traefik.http.routers.n8n.tls=true"
      - "traefik.http.routers.n8n.entrypoints=websecure" # Besser nur websecure nutzen, da globaler Redirect
      - "traefik.http.routers.n8n.tls.certresolver=mytlschallenge"
      # --- Hinzugefügte Header Middlewares ---
      - "traefik.http.middlewares.n8n-headers.headers.sslredirect=true"
      - "traefik.http.middlewares.n8n-headers.headers.stsseconds=31536000"
      - "traefik.http.middlewares.n8n-headers.headers.browserxssfilter=true"
      - "traefik.http.middlewares.n8n-headers.headers.contenttypenosniff=true"
      - "traefik.http.middlewares.n8n-headers.headers.forcestsheader=true"
      - "traefik.http.middlewares.n8n-headers.headers.sslhost=${DOMAIN_NAME}" # Kann entfallen, wenn sslredirect=true
      - "traefik.http.middlewares.n8n-headers.headers.stsincludesubdomains=true"
      # WICHTIG: Middleware an Router binden
      - "traefik.http.routers.n8n.middlewares=n8n-headers@docker" # <--- Hinzugefügt!
      # --- Service Definition ---
      - "traefik.http.services.n8n.loadbalancer.server.port=5678" # Der interne Container-Port von n8n
    environment:
      N8N_HOST=${SUBDOMAIN}.${DOMAIN_NAME}
      N8N_PROTOCOL=https
      NODE_ENV=production
      WEBHOOK_URL=${SUBDOMAIN}.${DOMAIN_NAME} # Nur Domain, kein Schema
      GENERIC_TIMEZONE=${GENERIC_TIMEZONE} # Sicherstellen, dass GENERIC_TIMEZONE gesetzt ist
    volumes:
      - n8n_data:/home/<USER>/.n8n # <--- NAMED VOLUME für persistente Daten
      - ./local-files:/files # <--- NAMED VOLUME für lokale Dateien (falls genutzt)
    networks: # <--- WICHTIG: Service muss im Netzwerk sein
      - webproxy # <--- Container ist Teil dieses Netzwerks

# --- WICHTIG: Korrekte Definition von Netzwerken und Volumes ---

# Definition der NAMED VOLUMES
volumes:
  traefik_data: # Dies ist der Name des Volumes für Traefik
    external: true # Sagt Docker, dass dieses Volume bereits existieren sollte (oder erstellt wird)
  n8n_data: # Dies ist der Name des Volumes für n8n
    external: true
  # Wenn du ./local-files als Volume nutzt und es auch ein named volume sein soll,
  # müsstest du hier 'local-files:' definieren. Ansonsten ist './local-files' ein Bind-Mount.
  # Für n8n wäre es eher:
  # n8n_local_files: # <-- Name für dieses Volume
  #   external: true # oder false, wenn es hier verwaltet wird

# Definition der NETZWERKE
networks:
  webproxy: # Dies ist der Name des Netzwerks, das du verwenden möchtest
    external: true # Sagt Docker, dass dieses Netzwerk bereits existiert (muss einmalig erstellt werden)